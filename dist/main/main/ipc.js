"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupIpcHandlers = setupIpcHandlers;
const electron_1 = require("electron");
function setupIpcHandlers(notesDB, clipboardManager, mainWindow) {
    // Notes operations
    electron_1.ipcMain.handle('notes:create', async (_event, content, title, parentId, isTemporary) => {
        try {
            const noteId = await notesDB.createNote(content, title, parentId, isTemporary);
            return noteId;
        }
        catch (error) {
            console.error('Error creating note:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:createFolder', async (_event, name, parentId, isTemporary) => {
        try {
            const folderId = await notesDB.createFolder(name, parentId, isTemporary);
            return folderId;
        }
        catch (error) {
            console.error('Error creating folder:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:update', async (_event, id, content, title) => {
        try {
            await notesDB.updateNote(id, content, title);
        }
        catch (error) {
            console.error('Error updating note:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:delete', async (_event, id) => {
        try {
            await notesDB.deleteNote(id);
        }
        catch (error) {
            console.error('Error deleting note:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:search', async (_event, query) => {
        try {
            const notes = await notesDB.searchNotes(query);
            return notes;
        }
        catch (error) {
            console.error('Error searching notes:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:getAll', async (_event) => {
        try {
            const notes = await notesDB.getAllNotes();
            return notes;
        }
        catch (error) {
            console.error('Error getting all notes:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:getTree', async (_event) => {
        try {
            const tree = await notesDB.getNotesTree();
            return tree;
        }
        catch (error) {
            console.error('Error getting notes tree:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:move', async (_event, noteId, newParentId) => {
        try {
            await notesDB.moveNote(noteId, newParentId);
        }
        catch (error) {
            console.error('Error moving note:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:toggleFolder', async (_event, folderId) => {
        try {
            await notesDB.toggleFolder(folderId);
        }
        catch (error) {
            console.error('Error toggling folder:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('notes:getPath', async (_event, noteId) => {
        try {
            const path = await notesDB.getNotePath(noteId);
            return path;
        }
        catch (error) {
            console.error('Error getting note path:', error);
            throw error;
        }
    });
    // Clipboard operations
    electron_1.ipcMain.handle('clipboard:getHistory', async (_event) => {
        try {
            return clipboardManager.getHistory();
        }
        catch (error) {
            console.error('Error getting clipboard history:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('clipboard:clear', async (_event) => {
        try {
            clipboardManager.clearHistory();
        }
        catch (error) {
            console.error('Error clearing clipboard history:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('clipboard:copy', async (_event, content) => {
        try {
            clipboardManager.copyToClipboard(content);
        }
        catch (error) {
            console.error('Error copying to clipboard:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('clipboard:remove', async (_event, id) => {
        try {
            clipboardManager.removeItem(id);
        }
        catch (error) {
            console.error('Error removing clipboard item:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle('clipboard:search', async (_event, query) => {
        try {
            return clipboardManager.searchHistory(query);
        }
        catch (error) {
            console.error('Error searching clipboard history:', error);
            throw error;
        }
    });
    // Window operations
    electron_1.ipcMain.handle('window:hide', async (_event) => {
        mainWindow.hide();
    });
    electron_1.ipcMain.handle('window:show', async (_event) => {
        mainWindow.show();
        mainWindow.focus();
    });
    electron_1.ipcMain.handle('window:toggle', async (_event) => {
        if (mainWindow.isVisible() && mainWindow.isFocused()) {
            mainWindow.hide();
        }
        else {
            mainWindow.show();
            mainWindow.focus();
        }
    });
    // App operations
    electron_1.ipcMain.handle('app:quit', async (_event) => {
        require('electron').app.quit();
    });
    console.log('IPC handlers setup complete');
}
