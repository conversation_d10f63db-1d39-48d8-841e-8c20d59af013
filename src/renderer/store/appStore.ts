import { create } from 'zustand';
import { Note, ClipboardItem, NoteNode, SearchResult } from '../../shared/types';

interface AppState {
  // Notes state
  notes: Note[];
  notesTree: NoteNode[];
  selectedNote: Note | null;
  selectedNode: NoteNode | null;
  searchResults: SearchResult[];
  isLoadingNotes: boolean;
  isLoadingTree: boolean;
  
  // Clipboard state
  clipboardHistory: ClipboardItem[];
  isLoadingClipboard: boolean;
  
  // UI state
  sidebarCollapsed: boolean;
  
  // Actions
  setNotes: (notes: Note[]) => void;
  setNotesTree: (tree: NoteNode[]) => void;
  setSearchResults: (results: SearchResult[]) => void;
  addNote: (note: Note) => void;
  updateNote: (note: Note) => void;
  removeNote: (noteId: string) => void;
  setSelectedNote: (note: Note | null) => void;
  setSelectedNode: (node: NoteNode | null) => void;
  setLoadingNotes: (loading: boolean) => void;
  setLoadingTree: (loading: boolean) => void;
  
  setClipboardHistory: (history: ClipboardItem[]) => void;
  addClipboardItem: (item: ClipboardItem) => void;
  removeClipboardItem: (itemId: string) => void;
  setLoadingClipboard: (loading: boolean) => void;
  
  setSidebarCollapsed: (collapsed: boolean) => void;
  
  // Async actions
  loadNotes: () => Promise<void>;
  loadNotesTree: () => Promise<void>;
  loadClipboardHistory: () => Promise<void>;
  createNote: (content: string, title?: string, parentId?: string) => Promise<string>;
  createFolder: (name: string, parentId?: string) => Promise<string>;
  saveNote: (id: string, content: string, title?: string) => Promise<void>;
  deleteNote: (id: string) => Promise<void>;
  searchNotes: (query: string) => Promise<void>;
  clearClipboardHistory: () => Promise<void>;
}

export const useAppStore = create<AppState>((set, get) => ({
  // Initial state
  notes: [],
  notesTree: [],
  selectedNote: null,
  selectedNode: null,
  searchResults: [],
  isLoadingNotes: false,
  isLoadingTree: false,
  clipboardHistory: [],
  isLoadingClipboard: false,
  sidebarCollapsed: false,

  // Basic setters
  setNotes: (notes) => set({ notes }),
  setNotesTree: (notesTree) => set({ notesTree }),
  setSearchResults: (searchResults) => set({ searchResults }),
  addNote: (note) => set((state) => ({ notes: [note, ...state.notes] })),
  updateNote: (updatedNote) => set((state) => ({
    notes: state.notes.map(note => 
      note.id === updatedNote.id ? updatedNote : note
    ),
    selectedNote: state.selectedNote?.id === updatedNote.id ? updatedNote : state.selectedNote
  })),
  removeNote: (noteId) => set((state) => ({
    notes: state.notes.filter(note => note.id !== noteId),
    selectedNote: state.selectedNote?.id === noteId ? null : state.selectedNote
  })),
  setSelectedNote: (note) => set({ selectedNote: note }),
  setSelectedNode: (node) => set({ selectedNode: node }),
  setLoadingNotes: (loading) => set({ isLoadingNotes: loading }),
  setLoadingTree: (loading) => set({ isLoadingTree: loading }),

  setClipboardHistory: (history) => set({ clipboardHistory: history }),
  addClipboardItem: (item) => set((state) => ({ 
    clipboardHistory: [item, ...state.clipboardHistory] 
  })),
  removeClipboardItem: (itemId) => set((state) => ({
    clipboardHistory: state.clipboardHistory.filter(item => item.id !== itemId)
  })),
  setLoadingClipboard: (loading) => set({ isLoadingClipboard: loading }),

  setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),

  // Async actions
  loadNotes: async () => {
    const { setLoadingNotes, setNotes } = get();
    setLoadingNotes(true);
    
    try {
      const notes = await window.electronAPI.getAllNotes();
      setNotes(notes);
    } catch (error) {
      console.error('Failed to load notes:', error);
    } finally {
      setLoadingNotes(false);
    }
  },

  loadNotesTree: async () => {
    const { setLoadingTree, setNotesTree } = get();
    setLoadingTree(true);
    
    try {
      const tree = await window.electronAPI.getNotesTree();
      setNotesTree(tree);
    } catch (error) {
      console.error('Failed to load notes tree:', error);
    } finally {
      setLoadingTree(false);
    }
  },

  loadClipboardHistory: async () => {
    const { setLoadingClipboard, setClipboardHistory } = get();
    setLoadingClipboard(true);
    
    try {
      const history = await window.electronAPI.getClipboardHistory();
      setClipboardHistory(history);
    } catch (error) {
      console.error('Failed to load clipboard history:', error);
    } finally {
      setLoadingClipboard(false);
    }
  },

  createNote: async (content: string, title?: string, parentId?: string) => {
    try {
      const noteId = await window.electronAPI.createNote(content, title, parentId);
      
      // Create a temporary note object for immediate UI update
      const newNote: Note = {
        id: noteId,
        content,
        title: title || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        parent_id: parentId || 'root',
        is_folder: false,
        is_expanded: false,
        is_pinned: false
      };
      
      get().addNote(newNote);
      // Also reload the tree to reflect the changes
      get().loadNotesTree();
      return noteId;
    } catch (error) {
      console.error('Failed to create note:', error);
      throw error;
    }
  },

  createFolder: async (name: string, parentId?: string) => {
    try {
      const folderId = await window.electronAPI.createFolder(name, parentId);
      // Reload the tree to reflect the new folder
      get().loadNotesTree();
      return folderId;
    } catch (error) {
      console.error('Failed to create folder:', error);
      throw error;
    }
  },

  saveNote: async (id: string, content: string, title?: string) => {
    try {
      await window.electronAPI.updateNote(id, content, title);
      
      // Update the note in the store
      const updatedNote: Note = {
        id,
        content,
        title: title || '',
        created_at: get().notes.find(n => n.id === id)?.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      
      get().updateNote(updatedNote);
    } catch (error) {
      console.error('Failed to save note:', error);
      throw error;
    }
  },

  deleteNote: async (id: string) => {
    try {
      await window.electronAPI.deleteNote(id);
      get().removeNote(id);
    } catch (error) {
      console.error('Failed to delete note:', error);
      throw error;
    }
  },

  searchNotes: async (query: string) => {
    const { setLoadingNotes, setSearchResults } = get();
    setLoadingNotes(true);
    
    try {
      const results = await window.electronAPI.searchNotes(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Failed to search notes:', error);
    } finally {
      setLoadingNotes(false);
    }
  },

  clearClipboardHistory: async () => {
    try {
      await window.electronAPI.clearClipboardHistory();
      get().setClipboardHistory([]);
    } catch (error) {
      console.error('Failed to clear clipboard history:', error);
      throw error;
    }
  },
}));
