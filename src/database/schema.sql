-- Nexus Database Schema
-- SQLite database for storing notes, folders, and clipboard history

-- Notes table for storing user notes with hierarchical structure
CREATE TABLE IF NOT EXISTS notes (
  id TEXT PRIMARY KEY,
  content TEXT NOT NULL,
  title TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  parent_id TEXT REFERENCES notes(id), -- null for root notes
  is_folder BOOLEAN DEFAULT FALSE, -- true for folders, false for notes
  is_expanded BOOLEAN DEFAULT TRUE, -- for folder collapse/expand state
  tags TEXT, -- JSON array of tags
  is_deleted BOOLEAN DEFAULT FALSE,
  is_pinned BOOLEAN DEFAULT FALSE,
  is_temporary BOOLEAN DEFAULT FALSE
);

-- Keep folders table for backward compatibility but mark as deprecated
-- New tree structure uses notes table with is_folder flag
CREATE TABLE IF NOT EXISTS folders (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  parent_id TEXT REFERENCES folders(id),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  color TEXT DEFAULT '#3B82F6', -- Hex color for folder
  is_deleted BOOLEAN DEFAULT FALSE
);

-- Clipboard history table
CREATE TABLE IF NOT EXISTS clipboard_history (
  id TEXT PRIMARY KEY,
  content TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('text', 'image', 'file')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  size_bytes INTEGER,
  source_app TEXT, -- Which app the content came from
  is_favorite BOOLEAN DEFAULT FALSE
);

-- Search index for full-text search on notes
CREATE VIRTUAL TABLE IF NOT EXISTS notes_fts USING fts5(
  id UNINDEXED,
  title,
  content,
  tags
);

-- Triggers to keep FTS table in sync with notes table
CREATE TRIGGER IF NOT EXISTS notes_fts_insert AFTER INSERT ON notes BEGIN
  INSERT INTO notes_fts(id, title, content, tags) 
  VALUES (new.id, new.title, new.content, new.tags);
END;

CREATE TRIGGER IF NOT EXISTS notes_fts_update AFTER UPDATE ON notes BEGIN
  UPDATE notes_fts 
  SET title = new.title, content = new.content, tags = new.tags 
  WHERE id = new.id;
END;

CREATE TRIGGER IF NOT EXISTS notes_fts_delete AFTER DELETE ON notes BEGIN
  DELETE FROM notes_fts WHERE id = old.id;
END;

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notes_created_at ON notes(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notes_updated_at ON notes(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_notes_parent_id ON notes(parent_id); -- For tree queries
CREATE INDEX IF NOT EXISTS idx_notes_is_folder ON notes(is_folder); -- For filtering folders vs notes
CREATE INDEX IF NOT EXISTS idx_notes_is_deleted ON notes(is_deleted);
CREATE INDEX IF NOT EXISTS idx_notes_is_pinned ON notes(is_pinned);

CREATE INDEX IF NOT EXISTS idx_folders_parent_id ON folders(parent_id);
CREATE INDEX IF NOT EXISTS idx_folders_is_deleted ON folders(is_deleted);

CREATE INDEX IF NOT EXISTS idx_clipboard_created_at ON clipboard_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_clipboard_type ON clipboard_history(type);
CREATE INDEX IF NOT EXISTS idx_clipboard_is_favorite ON clipboard_history(is_favorite);

-- Insert default root folder as a note entry for new tree structure
INSERT OR IGNORE INTO notes (id, title, content, parent_id, is_folder, is_expanded) 
VALUES ('root', 'My Notes', '', NULL, TRUE, TRUE);
