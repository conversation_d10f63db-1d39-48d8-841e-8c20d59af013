import { useState, useEffect, useCallback, useMemo } from 'react';

interface UseAutoSaveOptions {
  delay?: number; // Debounce delay in milliseconds (default: 3000)
  onSave: (content: string, title: string) => Promise<void>;
}

interface UseAutoSaveReturn {
  lastSaved: Date | null;
  isSaving: boolean;
  saveStatus: 'idle' | 'saving' | 'saved' | 'error';
  saveOnBlur: () => Promise<void>;
  forceSave: () => Promise<void>;
}

export function useAutoSave(
  content: string,
  title: string,
  options: UseAutoSaveOptions
): UseAutoSaveReturn {
  const { delay = 3000, onSave } = options;
  
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [lastSavedContent, setLastSavedContent] = useState({ content, title });

  // Debounced save function
  const debouncedSave = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    
    return (content: string, title: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(async () => {
        await performSave(content, title);
      }, delay);
    };
  }, [delay]);

  // Actual save function
  const performSave = useCallback(async (content: string, title: string) => {
    // Don't save if content hasn't changed
    if (content === lastSavedContent.content && title === lastSavedContent.title) {
      return;
    }

    setIsSaving(true);
    setSaveStatus('saving');
    
    try {
      await onSave(content, title);
      setLastSaved(new Date());
      setLastSavedContent({ content, title });
      setSaveStatus('saved');
      
      // Reset status to idle after 2 seconds
      setTimeout(() => {
        setSaveStatus('idle');
      }, 2000);
    } catch (error) {
      console.error('Auto-save failed:', error);
      setSaveStatus('error');
      
      // Reset error status after 5 seconds
      setTimeout(() => {
        setSaveStatus('idle');
      }, 5000);
    } finally {
      setIsSaving(false);
    }
  }, [onSave, lastSavedContent]);

  // Auto-save on content change (debounced)
  useEffect(() => {
    if (content.trim() || title.trim()) {
      debouncedSave(content, title);
    }
  }, [content, title, debouncedSave]);

  // Save on blur (immediate)
  const saveOnBlur = useCallback(async () => {
    if (content !== lastSavedContent.content || title !== lastSavedContent.title) {
      await performSave(content, title);
    }
  }, [content, title, lastSavedContent, performSave]);

  // Force save (immediate)
  const forceSave = useCallback(async () => {
    await performSave(content, title);
  }, [content, title, performSave]);

  return {
    lastSaved,
    isSaving,
    saveStatus,
    saveOnBlur,
    forceSave
  };
}