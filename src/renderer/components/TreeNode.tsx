import React, { useState, useRef, useEffect } from 'react';
import { NoteNode } from '../../shared/types';

interface TreeNodeProps {
  node: NoteNode;
  level: number;
  selectedNodeId?: string;
  editingNodeId?: string;
  newlyCreatedNodeId?: string;
  onNodeSelect: (node: NoteNode) => void;
  onToggleFolder: (folderId: string) => void;
  onCreateNote: (parentId: string) => void;
  onCreateFolder: (parentId: string) => void;
  onDeleteNode: (nodeId: string) => void;
  onRenameNode: (nodeId: string, newName: string) => void;
  onStartEdit: (nodeId: string) => void;
  onFinishEdit: (nodeId: string, newName: string) => void;
  onCancelEdit: (nodeId: string) => void;
}

export function TreeNode({
  node,
  level,
  selectedNodeId,
  editingNodeId,
  newlyCreatedNodeId,
  onNodeSelect,
  onToggleFolder,
  onCreateNote,
  onCreateFolder,
  onDeleteNode,
  onRenameNode,
  onStartEdit,
  onFinishEdit,
  onCancelEdit
}: TreeNodeProps) {
  const [editValue, setEditValue] = useState(node.title);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const isEditing = editingNodeId === node.id;
  const isSelected = selectedNodeId === node.id;
  const isNewlyCreated = newlyCreatedNodeId === node.id;

  // Auto-focus and select all when entering edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // Update edit value when node title changes
  useEffect(() => {
    setEditValue(node.title);
  }, [node.title]);

  const handleToggleFolder = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (node.isFolder) {
      onToggleFolder(node.id);
    }
  };

  const handleNodeClick = () => {
    if (!isEditing) {
      if (!node.isFolder) {
        onNodeSelect(node);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isEditing) return; // Don't handle shortcuts while editing
    
    if (e.key === 'F2') {
      e.preventDefault();
      onStartEdit(node.id);
    } else if (e.key === 'Delete' || e.key === 'Backspace') {
      e.preventDefault();
      if (confirm(`Are you sure you want to delete "${node.title}"?`)) {
        onDeleteNode(node.id);
      }
    } else if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleNodeClick();
    }
  };

  const handleDoubleClick = () => {
    if (!isEditing) {
      onStartEdit(node.id);
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (editValue.trim()) {
        onFinishEdit(node.id, editValue.trim());
      } else {
        onCancelEdit(node.id);
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setEditValue(node.title);
      onCancelEdit(node.id);
    }
  };

  const handleBlur = () => {
    if (editValue.trim()) {
      onFinishEdit(node.id, editValue.trim());
    } else {
      onCancelEdit(node.id);
    }
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowContextMenu(true);
  };

  const indentStyle = {
    paddingLeft: `${level * 16 + 8}px`
  };

  // Node icon based on state
  const getNodeIcon = () => {
    if (isEditing) {
      return <span className="text-blue-500">✏️</span>;
    }
    if (node.isTemporary) {
      return <span className="text-yellow-500">⏳</span>;
    }
    if (node.isFolder) {
      return <span className="text-blue-500">📁</span>;
    }
    return <span className="text-gray-400">📄</span>;
  };

  return (
    <>
      <div
        className={`flex items-center py-1 px-2 transition-colors group relative select-none ${
          !isEditing ? 'cursor-pointer hover:bg-gray-100' : ''
        } ${
          isSelected ? 'bg-nexus-50 text-nexus-600' : 'text-gray-700'
        } ${
          isEditing ? 'editing-item' : ''
        } ${
          node.isTemporary && !isEditing ? 'temporary-item' : ''
        } ${
          isNewlyCreated && !isEditing ? 'newly-created' : ''
        }`}
        style={indentStyle}
        tabIndex={0}
        onClick={handleNodeClick}
        onDoubleClick={handleDoubleClick}
        onContextMenu={handleContextMenu}
        onKeyDown={handleKeyDown}
      >
        {/* Expand/Collapse Icon */}
        {node.isFolder && (
          <button
            onClick={handleToggleFolder}
            className="mr-1 p-0.5 rounded hover:bg-gray-200 transition-colors"
          >
            <svg
              className={`w-3 h-3 transition-transform ${node.isExpanded ? 'rotate-90' : ''}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        )}

        {/* Node Icon */}
        <div className="mr-2 flex-shrink-0">
          {getNodeIcon()}
        </div>

        {/* Title or Input */}
        {isEditing ? (
          <input
            ref={inputRef}
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleInputKeyDown}
            onBlur={handleBlur}
            className="flex-1 px-2 py-1 text-sm border border-blue-500 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-600 bg-white select-text"
            maxLength={255}
            autoComplete="off"
            spellCheck={false}
          />
        ) : (
          <span
            className={`flex-1 text-sm truncate ${node.isPinned ? 'font-semibold' : ''} ${
              node.isTemporary ? 'text-gray-500 italic' : ''
            }`}
          >
            {node.title}
            {node.isPinned && (
              <span className="ml-1 text-yellow-500">📌</span>
            )}
          </span>
        )}

        {/* Context Menu Trigger - only show when not editing */}
        {!isEditing && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowContextMenu(!showContextMenu);
            }}
            className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-gray-200 transition-all"
          >
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
            </svg>
          </button>
        )}

        {/* Context Menu */}
        {showContextMenu && !isEditing && (
          <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
            {node.isFolder ? (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onCreateNote(node.id);
                    setShowContextMenu(false);
                  }}
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
                >
                  📄 New Note
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onCreateFolder(node.id);
                    setShowContextMenu(false);
                  }}
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
                >
                  📁 New Folder
                </button>
                <hr className="my-1" />
              </>
            ) : null}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onStartEdit(node.id);
                setShowContextMenu(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
            >
              ✏️ Rename
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (confirm(`Are you sure you want to delete "${node.title}"?`)) {
                  onDeleteNode(node.id);
                }
                setShowContextMenu(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-red-50 text-red-600 flex items-center"
            >
              🗑️ Delete
            </button>
          </div>
        )}
      </div>

      {/* Click outside to close context menu */}
      {showContextMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowContextMenu(false)}
        />
      )}

      {/* Render children */}
      {node.isFolder && node.isExpanded && node.children.length > 0 && (
        <div>
          {node.children.map((child) => (
            <TreeNode
              key={child.id}
              node={child}
              level={level + 1}
              selectedNodeId={selectedNodeId}
              editingNodeId={editingNodeId}
              newlyCreatedNodeId={newlyCreatedNodeId}
              onNodeSelect={onNodeSelect}
              onToggleFolder={onToggleFolder}
              onCreateNote={onCreateNote}
              onCreateFolder={onCreateFolder}
              onDeleteNode={onDeleteNode}
              onRenameNode={onRenameNode}
              onStartEdit={onStartEdit}
              onFinishEdit={onFinishEdit}
              onCancelEdit={onCancelEdit}
            />
          ))}
        </div>
      )}
    </>
  );
}