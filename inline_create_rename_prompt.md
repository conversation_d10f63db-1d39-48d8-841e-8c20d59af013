# Inline Create & Rename Feature Requirements

## Overview
Implement minipad2-style instant creation and inline renaming behavior for notes and folders in the tree view. When user clicks "New Note" or "New Folder", immediately create the item in the current directory and enter inline editing mode for naming.

## Current State vs. Required Behavior

### Current Behavior (Problematic)
```
User clicks "New Note" → Modal dialog appears → User types name → Clicks OK → Note created
```

### Required Behavior (minipad2-style)
```
User clicks "New Note" → Note immediately appears as "Untitled" → Text is selected for editing → User types new name → Presses Enter or clicks away → Name is saved
```

## 1. Instant Creation with Inline Editing

### 1.1 New Note Creation Flow
```typescript
// When user clicks "New Note" button or uses shortcut
const createNewNote = async (parentFolderId?: string) => {
  // 1. Immediately create note in database with temporary name
  const newNote = await createNote({
    title: "Untitled",
    content: "",
    parentId: parentFolderId || currentFolderId,
    isTemporary: true // Flag for cleanup if user cancels
  });
  
  // 2. Add to tree view and immediately enter edit mode
  const treeNode = addNoteToTree(newNote, parentFolderId);
  enterInlineEditMode(treeNode);
  
  // 3. Select all text for immediate typing
  selectAllText(treeNode.textInput);
};
```

### 1.2 New Folder Creation Flow  
```typescript
const createNewFolder = async (parentFolderId?: string) => {
  // 1. Create folder immediately
  const newFolder = await createFolder({
    name: "New Folder",
    parentId: parentFolderId || currentFolderId,
    isTemporary: true
  });
  
  // 2. Add to tree and enter edit mode
  const treeNode = addFolderToTree(newFolder, parentFolderId);
  enterInlineEditMode(treeNode);
  selectAllText(treeNode.textInput);
};
```

## 2. Inline Editing Implementation

### 2.1 TreeNode Component Structure
```tsx
interface TreeNodeProps {
  node: NoteNode;
  isEditing: boolean;
  onStartEdit: () => void;
  onFinishEdit: (newName: string) => void;
  onCancelEdit: () => void;
}

const TreeNode: React.FC<TreeNodeProps> = ({ 
  node, 
  isEditing, 
  onStartEdit, 
  onFinishEdit, 
  onCancelEdit 
}) => {
  const [editValue, setEditValue] = useState(node.title || node.name);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Auto-focus and select all when entering edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onFinishEdit(editValue.trim());
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancelEdit();
    }
  };
  
  const handleBlur = () => {
    if (editValue.trim()) {
      onFinishEdit(editValue.trim());
    } else {
      onCancelEdit();
    }
  };
  
  return (
    <div className="tree-node">
      <div className="node-content">
        {node.isFolder ? '📁' : '📄'}
        
        {isEditing ? (
          <input
            ref={inputRef}
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            className="inline-edit-input"
            maxLength={255}
          />
        ) : (
          <span 
            className="node-label"
            onDoubleClick={onStartEdit}
          >
            {node.title || node.name}
          </span>
        )}
      </div>
      
      {node.children && node.isExpanded && (
        <div className="node-children">
          {node.children.map(child => (
            <TreeNode key={child.id} node={child} {...childProps} />
          ))}
        </div>
      )}
    </div>
  );
};
```

### 2.2 Edit Mode Styling
```css
.inline-edit-input {
  border: 1px solid #007acc;
  border-radius: 2px;
  padding: 1px 4px;
  font-size: inherit;
  font-family: inherit;
  background: white;
  outline: none;
  width: 100%;
  min-width: 100px;
}

.inline-edit-input:focus {
  border-color: #0078d4;
  box-shadow: 0 0 0 1px rgba(0, 120, 212, 0.3);
}

.node-label:hover {
  background-color: rgba(0, 0, 0, 0.04);
  cursor: pointer;
}

.tree-node {
  user-select: none; /* Prevent text selection on tree nodes */
}

.inline-edit-input {
  user-select: text; /* Allow text selection in edit mode */
}
```

## 3. Context Integration

### 3.1 Current Directory Awareness
```typescript
const TreeContext = createContext({
  currentFolderId: null as string | null,
  editingNodeId: null as string | null,
  setEditingNode: (nodeId: string | null) => {},
});

const useTreeContext = () => {
  const context = useContext(TreeContext);
  if (!context) {
    throw new Error('useTreeContext must be used within TreeProvider');
  }
  return context;
};
```

### 3.2 New Item Placement Logic
```typescript
const getNewItemParent = (selectedNodeId?: string): string | null => {
  if (!selectedNodeId) return null;
  
  const selectedNode = findNodeById(selectedNodeId);
  if (!selectedNode) return null;
  
  // If selected node is a folder, create inside it
  if (selectedNode.isFolder) {
    return selectedNode.id;
  }
  
  // If selected node is a note, create in same parent
  return selectedNode.parentId;
};
```

## 4. Keyboard Shortcuts & UX

### 4.1 Keyboard Shortcuts (macOS)
```typescript
// For Electron on macOS
const registerMacOSShortcuts = (window: BrowserWindow) => {
  // Global shortcuts using Command key (⌘)
  globalShortcut.register('CommandOrControl+N', () => {
    window.webContents.send('create-new-note');
  });
  
  globalShortcut.register('CommandOrControl+Shift+N', () => {
    window.webContents.send('create-new-folder');
  });
  
  // Note: Enter and Backspace are handled in the renderer process
  // as they are context-specific to the tree view component
};

// In renderer process - macOS-specific behavior
const handleKeyDown = (e: React.KeyboardEvent) => {
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  
  if (e.key === 'Enter' && !e.shiftKey && !e.altKey && !e.ctrlKey && !(isMac && e.metaKey)) {
    // Enter key starts rename on macOS (Finder behavior)
    e.preventDefault();
    startRenameSelected();
  } else if (e.key === 'Backspace' && !e.shiftKey && !e.altKey && !e.ctrlKey && !(isMac && e.metaKey)) {
    // Backspace deletes on macOS (Finder behavior)
    e.preventDefault();
    deleteSelected();
  } else if (e.key === ' ' && !e.shiftKey && !e.altKey && !e.ctrlKey && !(isMac && e.metaKey)) {
    // Space for Quick Look style preview
    e.preventDefault();
    previewSelected();
  }
  
  // Handle inline editing keys
  if (isInEditMode) {
    if (e.key === 'Enter') {
      e.preventDefault();
      finishEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEdit();
    }
  }
};
```

### 4.2 Context Menu Integration
```tsx
const ContextMenu = ({ node, position }) => (
  <div className="context-menu" style={{ top: position.y, left: position.x }}>
    <MenuItem onClick={() => createNewNote(node.id)}>
      📄 New Note
    </MenuItem>
    <MenuItem onClick={() => createNewFolder(node.id)}>
      📁 New Folder
    </MenuItem>
    <MenuSeparator />
    <MenuItem onClick={() => startRename(node.id)}>
      ✏️ Rename
    </MenuItem>
    <MenuItem onClick={() => deleteNode(node.id)}>
      🗑️ Delete
    </MenuItem>
  </div>
);
```

## 5. Data Management

### 5.1 Temporary Item Cleanup
```typescript
const handleEditComplete = async (nodeId: string, newName: string) => {
  if (!newName.trim()) {
    // User provided empty name - delete the temporary item
    await deleteNote(nodeId);
    removeFromTree(nodeId);
    return;
  }
  
  // Update with final name and mark as permanent
  await updateNote(nodeId, {
    title: newName.trim(),
    isTemporary: false
  });
  
  // Update tree display
  updateTreeNode(nodeId, { title: newName.trim() });
  setEditingNode(null);
};

const handleEditCancel = async (nodeId: string) => {
  const node = findNodeById(nodeId);
  if (node?.isTemporary) {
    // Delete temporary item
    await deleteNote(nodeId);
    removeFromTree(nodeId);
  }
  setEditingNode(null);
};
```

### 5.2 Duplicate Name Handling
```typescript
const generateUniqueName = (baseName: string, parentId: string | null): string => {
  const siblings = getChildNodes(parentId);
  const existingNames = siblings.map(s => s.title || s.name);
  
  if (!existingNames.includes(baseName)) {
    return baseName;
  }
  
  let counter = 1;
  let newName: string;
  do {
    newName = `${baseName} (${counter})`;
    counter++;
  } while (existingNames.includes(newName));
  
  return newName;
};
```

## 6. Visual Feedback & States

### 6.1 Creation Animation
```css
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.tree-node.newly-created {
  animation: slideInFromLeft 0.2s ease-out;
}
```

### 6.2 Edit State Indicators
```tsx
const NodeIcon = ({ node, isEditing }) => {
  if (isEditing) {
    return <span className="edit-indicator">✏️</span>;
  }
  
  if (node.isTemporary) {
    return <span className="temp-indicator">⏳</span>;
  }
  
  return node.isFolder ? '📁' : '📄';
};
```

## 7. Error Handling & Edge Cases

### 7.1 Name Validation
```typescript
const validateNodeName = (name: string): { isValid: boolean; error?: string } => {
  if (!name.trim()) {
    return { isValid: false, error: 'Name cannot be empty' };
  }
  
  if (name.length > 255) {
    return { isValid: false, error: 'Name too long (max 255 characters)' };
  }
  
  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(name)) {
    return { isValid: false, error: 'Name contains invalid characters' };
  }
  
  return { isValid: true };
};
```

### 7.2 Concurrent Edit Prevention
```typescript
const useEditLock = () => {
  const [editingNodes, setEditingNodes] = useState<Set<string>>(new Set());
  
  const startEdit = (nodeId: string): boolean => {
    if (editingNodes.has(nodeId)) return false;
    
    setEditingNodes(prev => new Set([...prev, nodeId]));
    return true;
  };
  
  const endEdit = (nodeId: string) => {
    setEditingNodes(prev => {
      const next = new Set(prev);
      next.delete(nodeId);
      return next;
    });
  };
  
  return { startEdit, endEdit, isEditing: (nodeId: string) => editingNodes.has(nodeId) };
};
```

## 8. Implementation Checklist

### Phase 1: Basic Inline Creation
- [ ] Implement instant note/folder creation
- [ ] Add inline editing component
- [ ] Handle Enter/Escape/Blur events
- [ ] Add text selection on edit start

### Phase 2: Tree Integration  
- [ ] Integrate with existing tree view
- [ ] Add current directory context
- [ ] Implement parent folder detection
- [ ] Add visual feedback for new items

### Phase 3: Polish & UX
- [ ] Add keyboard shortcuts (Ctrl+N, F2, etc.)
- [ ] Implement context menu integration
- [ ] Add creation animations
- [ ] Handle name validation and duplicates

### Phase 4: Edge Cases
- [ ] Cleanup temporary items on app close
- [ ] Handle concurrent editing scenarios
- [ ] Add proper error handling
- [ ] Implement undo/redo for renames

## Expected User Experience

1. **Instant Gratification**: Click "New Note" → item appears immediately
2. **Intuitive Naming**: Text is pre-selected, ready for typing
3. **Forgiving**: Empty name cancels creation, no permanent empty items
4. **Consistent**: Same behavior for both notes and folders
5. **Keyboard Friendly**: All operations accessible via shortcuts
6. **Visual Clarity**: Clear indicators for edit state and temporary items

This implementation recreates the responsive, intuitive behavior that made minipad2 popular while modernizing it for contemporary web technologies.