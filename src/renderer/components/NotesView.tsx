import React, { useEffect, useState, useCallback } from 'react';
import { Note, NoteNode } from '../../shared/types';
import { useAppStore } from '../store/appStore';
import { TreeSidebar } from './TreeSidebar';
import { useAutoSave } from '../hooks/useAutoSave';

interface NotesViewProps {
  searchQuery: string;
  selectedNote: Note | null;
  onSelectNote: (note: Note | null) => void;
}

export function NotesView({ searchQuery, selectedNote, onSelectNote }: NotesViewProps) {
  const {
    selectedNode,
    setSelectedNode,
    createNote,
    saveNote,
    deleteNote
  } = useAppStore();

  const [editorContent, setEditorContent] = useState('');
  const [editorTitle, setEditorTitle] = useState('');
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [currentParentId, setCurrentParentId] = useState<string | undefined>();

  // Auto-save functionality  
  const autoSave = useAutoSave(editorContent, editorTitle, {
    delay: 3000, // 3 second delay for typing
    onSave: async (content: string, title: string) => {
      if (selectedNode && !isCreatingNew) {
        console.log('Auto-saving existing note:', selectedNode.id);
        await saveNote(selectedNode.id, content, title || undefined);
      } else if (isCreatingNew && (content.trim() || title.trim())) {
        console.log('Auto-saving new note');
        const noteId = await createNote(content, title || undefined, currentParentId);
        setIsCreatingNew(false);
        setCurrentParentId(undefined);
        console.log('Auto-created note with ID:', noteId);
      }
    }
  });

  // Update editor when selected node changes
  useEffect(() => {
    if (selectedNode && !selectedNode.isFolder) {
      setEditorContent(selectedNode.content);
      setEditorTitle(selectedNode.title || '');
      setIsCreatingNew(false);
      // Update the parent's selectedNote for compatibility
      const note: Note = {
        id: selectedNode.id,
        content: selectedNode.content,
        title: selectedNode.title,
        created_at: selectedNode.createdAt.toISOString(),
        updated_at: selectedNode.updatedAt.toISOString(),
        parent_id: selectedNode.parentId,
        is_folder: selectedNode.isFolder,
        is_expanded: selectedNode.isExpanded,
        is_pinned: selectedNode.isPinned
      };
      onSelectNote(note);
    } else if (!isCreatingNew) {
      setEditorContent('');
      setEditorTitle('');
      onSelectNote(null);
    }
  }, [selectedNode, isCreatingNew, onSelectNote]);

  const handleNewNote = useCallback((parentId?: string) => {
    setSelectedNode(null);
    onSelectNote(null);
    setEditorContent('');
    setEditorTitle('');
    setIsCreatingNew(true);
    setCurrentParentId(parentId);
  }, [onSelectNote, setSelectedNode]);

  // Listen for new note events
  useEffect(() => {
    if (window.electronEvents) {
      window.electronEvents.onCreateNewNote(() => handleNewNote());
      
      return () => {
        window.electronEvents.removeAllListeners('create-new-note');
      };
    }
  }, [handleNewNote]);

  const handleNodeSelect = useCallback((node: NoteNode) => {
    if (!node.isFolder) {
      setSelectedNode(node);
    }
  }, [setSelectedNode]);


  const handleSaveNote = async () => {
    if (!editorContent.trim() && !editorTitle.trim()) return;

    try {
      if (isCreatingNew) {
        // Create new note
        console.log('Creating new note:', { content: editorContent, title: editorTitle, parentId: currentParentId });
        const noteId = await createNote(editorContent, editorTitle || undefined, currentParentId);
        setIsCreatingNew(false);
        setCurrentParentId(undefined);
        console.log('Note created with ID:', noteId);
      } else if (selectedNode) {
        // Update existing note
        console.log('Updating note:', selectedNode.id, { content: editorContent, title: editorTitle });
        await saveNote(selectedNode.id, editorContent, editorTitle || undefined);
        console.log('Note updated successfully');
      }
    } catch (error) {
      console.error('Failed to save note:', error);
    }
  };

  const handleDeleteNote = async () => {
    if (!selectedNode) return;

    if (confirm('Are you sure you want to delete this note?')) {
      try {
        await deleteNote(selectedNode.id);
        setSelectedNode(null);
        onSelectNote(null);
      } catch (error) {
        console.error('Failed to delete note:', error);
        // TODO: Show error toast
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  console.log('NotesView rendering, about to render TreeSidebar with:', { searchQuery, selectedNodeId: selectedNode?.id });
  
  return (
    <div className="flex h-full">
      {/* DEBUG: About to render TreeSidebar */}
      <div style={{backgroundColor: 'green', color: 'white', padding: '5px', fontSize: '16px'}}>
        🔧 NOTESVIEW RENDERING TREESIDEBAR
      </div>
      {/* Tree Sidebar */}
      <TreeSidebar
        searchQuery={searchQuery}
        selectedNodeId={selectedNode?.id}
        onNodeSelect={handleNodeSelect}
      />

      {/* Editor */}
      <div className="flex-1 flex flex-col bg-white">
        {selectedNode || isCreatingNew ? (
          <>
            {/* Editor Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <input
                  type="text"
                  value={editorTitle}
                  onChange={(e) => setEditorTitle(e.target.value)}
                  onBlur={autoSave.saveOnBlur}
                  placeholder="Note title..."
                  className="text-lg font-semibold text-gray-900 bg-transparent border-none outline-none flex-1"
                />
                <div className="flex items-center space-x-2">
                  {/* Save Status Indicator */}
                  {autoSave.saveStatus !== 'idle' && (
                    <div className="flex items-center text-sm text-gray-500">
                      {autoSave.saveStatus === 'saving' && (
                        <>
                          <svg className="animate-spin w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
                          </svg>
                          Saving...
                        </>
                      )}
                      {autoSave.saveStatus === 'saved' && (
                        <>
                          <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          Saved
                        </>
                      )}
                      {autoSave.saveStatus === 'error' && (
                        <>
                          <svg className="w-4 h-4 mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          Error
                        </>
                      )}
                    </div>
                  )}
                  
                  <button
                    onClick={() => {
                      console.log('Quick New Note button clicked');
                      handleNewNote();
                    }}
                    className="btn-secondary text-sm py-1 px-3 mr-2"
                  >
                    New Note
                  </button>
                  <button
                    onClick={autoSave.forceSave}
                    className="btn-primary text-sm py-1 px-3"
                    disabled={autoSave.isSaving}
                  >
                    {autoSave.isSaving ? 'Saving...' : 'Save'}
                  </button>
                  {selectedNode && (
                    <button
                      onClick={handleDeleteNote}
                      className="btn-secondary text-sm py-1 px-3 text-red-600 hover:bg-red-50"
                    >
                      Delete
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Editor Content */}
            <div className="flex-1 p-4">
              <textarea
                value={editorContent}
                onChange={(e) => setEditorContent(e.target.value)}
                onBlur={autoSave.saveOnBlur}
                placeholder="Start writing..."
                className="editor-textarea"
                autoFocus={isCreatingNew}
              />
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-lg mb-2">Select a note to edit</p>
              <p className="text-sm mb-4">or create a new one to get started</p>
              <button
                onClick={() => {
                  console.log('🔥 MAIN NEW NOTE BUTTON CLICKED!');
                  handleNewNote();
                }}
                className="btn-primary py-3 px-6 text-lg font-semibold"
                style={{backgroundColor: '#007AFF', color: 'white', borderRadius: '8px', border: 'none', cursor: 'pointer'}}
              >
                🔥 CREATE NEW NOTE
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
