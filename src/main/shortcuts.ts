import { globalShortcut, BrowserWindow, app } from 'electron';

export function registerShortcuts(window: BrowserWindow) {
  // Main toggle shortcut - Cmd+Shift+Space
  const toggleShortcut = 'CommandOrControl+Shift+Space';
  
  const registerResult = globalShortcut.register(toggleShortcut, () => {
    toggleWindow(window);
  });

  if (!registerResult) {
    console.error('Failed to register global shortcut:', toggleShortcut);
  } else {
    console.log('Global shortcut registered:', toggleShortcut);
  }

  // Quick note shortcut - Cmd+Shift+N
  const quickNoteShortcut = 'CommandOrControl+Shift+N';
  
  const quickNoteResult = globalShortcut.register(quickNoteShortcut, () => {
    showWindowAndCreateNote(window);
  });

  if (!quickNoteResult) {
    console.error('Failed to register quick note shortcut:', quickNoteShortcut);
  } else {
    console.log('Quick note shortcut registered:', quickNoteShortcut);
  }

  // Calculator shortcut - Cmd+Shift+C
  const calculatorShortcut = 'CommandOrControl+Shift+C';
  
  const calculatorResult = globalShortcut.register(calculatorShortcut, () => {
    showWindowAndOpenCalculator(window);
  });

  if (!calculatorResult) {
    console.error('Failed to register calculator shortcut:', calculatorShortcut);
  } else {
    console.log('Calculator shortcut registered:', calculatorShortcut);
  }

  // Clipboard history shortcut - Cmd+Shift+V
  const clipboardShortcut = 'CommandOrControl+Shift+V';
  
  const clipboardResult = globalShortcut.register(clipboardShortcut, () => {
    showWindowAndOpenClipboard(window);
  });

  if (!clipboardResult) {
    console.error('Failed to register clipboard shortcut:', clipboardShortcut);
  } else {
    console.log('Clipboard shortcut registered:', clipboardShortcut);
  }

  // Search shortcut - Cmd+K
  const searchShortcut = 'CommandOrControl+K';
  
  const searchResult = globalShortcut.register(searchShortcut, () => {
    showWindowAndFocusSearch(window);
  });

  if (!searchResult) {
    console.error('Failed to register search shortcut:', searchShortcut);
  } else {
    console.log('Search shortcut registered:', searchShortcut);
  }

  // Cleanup shortcuts when app is quitting
  app.on('will-quit', () => {
    globalShortcut.unregisterAll();
  });
}

function toggleWindow(window: BrowserWindow) {
  console.log(`Toggle window - visible: ${window.isVisible()}, focused: ${window.isFocused()}, loading: ${window.webContents.isLoading()}`);
  
  if (window.isVisible() && window.isFocused()) {
    console.log('Hiding window');
    window.hide();
    
    // Hide from dock on macOS
    if (process.platform === 'darwin') {
      app.dock.hide();
    }
  } else {
    console.log('Showing window');
    showWindow(window);
  }
}

function showWindow(window: BrowserWindow) {
  // Ensure window is ready before showing
  if (window.webContents.isLoading()) {
    console.log('Window content is loading, waiting for completion...');
    window.webContents.once('did-finish-load', () => {
      console.log('Content loaded, showing window');
      showWindowImmediate(window);
    });
  } else {
    showWindowImmediate(window);
  }
}

function showWindowImmediate(window: BrowserWindow) {
  console.log('showWindowImmediate called');
  
  // Restore window if minimized
  if (window.isMinimized()) {
    console.log('Window was minimized, restoring');
    window.restore();
  }
  
  console.log('Showing and focusing window');
  window.show();
  window.focus();
  
  // Bring to front on macOS and ensure it's visible
  if (process.platform === 'darwin') {
    app.dock.show();
    // Force the window to the foreground
    app.focus({ steal: true });
  }
  
  // Wait a bit and check if window is actually visible
  setTimeout(() => {
    console.log(`Window state after show: visible=${window.isVisible()}, focused=${window.isFocused()}`);
  }, 100);
  
  console.log('Window shown and focused');
}

function showWindowAndCreateNote(window: BrowserWindow) {
  showWindow(window);
  
  // Send event to renderer to create a new note
  window.webContents.send('create-new-note');
}

function showWindowAndOpenCalculator(window: BrowserWindow) {
  showWindow(window);
  
  // Send event to renderer to switch to calculator
  window.webContents.send('switch-to-calculator');
}

function showWindowAndOpenClipboard(window: BrowserWindow) {
  showWindow(window);
  
  // Send event to renderer to switch to clipboard
  window.webContents.send('switch-to-clipboard');
}

function showWindowAndFocusSearch(window: BrowserWindow) {
  showWindow(window);
  
  // Send event to renderer to focus search input
  window.webContents.send('focus-search');
}
