"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const tray_1 = require("./tray");
const shortcuts_1 = require("./shortcuts");
const ipc_1 = require("./ipc");
const notes_1 = require("../database/notes");
const clipboard_1 = require("./clipboard");
class NexusApp {
    constructor() {
        this.mainWindow = null;
        this.tray = null;
        this.notesDB = null;
        this.clipboardManager = null;
        this.setupApp();
    }
    setupApp() {
        // Ensure single instance
        const gotTheLock = electron_1.app.requestSingleInstanceLock();
        if (!gotTheLock) {
            electron_1.app.quit();
            return;
        }
        // Handle second instance attempt
        electron_1.app.on('second-instance', () => {
            // Someone tried to run a second instance, focus our window instead
            if (this.mainWindow) {
                if (this.mainWindow.isMinimized()) {
                    this.mainWindow.restore();
                }
                this.mainWindow.show();
                this.mainWindow.focus();
            }
        });
        // Handle app ready
        electron_1.app.whenReady().then(() => {
            this.createWindow();
            this.setupTray();
            this.setupShortcuts();
            this.setupDatabase();
            this.setupClipboard();
            this.setupIPC();
        });
        // Handle window closed
        electron_1.app.on('window-all-closed', () => {
            // On macOS, keep app running even when all windows are closed
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        // Handle app activation (macOS)
        electron_1.app.on('activate', () => {
            if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                this.createWindow();
            }
        });
        // Handle before quit
        electron_1.app.on('before-quit', () => {
            console.log('App is quitting...');
            if (this.clipboardManager) {
                this.clipboardManager.stop();
            }
            if (this.tray) {
                this.tray.destroy();
            }
        });
    }
    createWindow() {
        // Prevent creating multiple windows
        if (this.mainWindow) {
            this.mainWindow.show();
            this.mainWindow.focus();
            return;
        }
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 600,
            minHeight: 400,
            titleBarStyle: 'hiddenInset',
            trafficLightPosition: { x: -100, y: -100 }, // Hide traffic lights
            backgroundColor: '#ffffff', // White background
            show: false, // Don't show until ready
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: (0, path_1.join)(__dirname, '../preload/preload.js'),
                webSecurity: false, // Disable for localhost in development
            },
        });
        // Load the React app
        if (process.env.NODE_ENV === 'development') {
            this.mainWindow.loadURL('http://localhost:5173');
            // Auto-open DevTools for debugging
            this.mainWindow.webContents.openDevTools();
        }
        else {
            this.mainWindow.loadFile((0, path_1.join)(__dirname, '../../renderer/index.html'));
        }
        // Handle window events
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
        // Hide window instead of closing on macOS
        this.mainWindow.on('close', (event) => {
            if (process.platform === 'darwin') {
                event.preventDefault();
                this.mainWindow?.hide();
            }
        });
        // Handle content loading
        this.mainWindow.webContents.on('did-finish-load', () => {
            console.log('Renderer content loaded');
            // Show and focus the window after content loads
            this.mainWindow?.show();
            this.mainWindow?.focus();
            this.mainWindow?.moveTop();
        });
        this.mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
            console.error('Failed to load renderer:', errorCode, errorDescription, validatedURL);
        });
        // Window is already shown, just log when it's ready
        this.mainWindow.once('ready-to-show', () => {
            console.log('Window ready to show');
        });
    }
    setupTray() {
        if (this.mainWindow) {
            this.tray = new tray_1.SimpleTray(this.mainWindow);
        }
    }
    setupShortcuts() {
        if (this.mainWindow) {
            (0, shortcuts_1.registerShortcuts)(this.mainWindow);
        }
    }
    setupDatabase() {
        const dbPath = (0, path_1.join)(electron_1.app.getPath('userData'), 'nexus.db');
        this.notesDB = new notes_1.NotesDB(dbPath);
    }
    setupClipboard() {
        this.clipboardManager = new clipboard_1.ClipboardManager();
        this.clipboardManager.start();
    }
    setupIPC() {
        if (this.notesDB && this.clipboardManager && this.mainWindow) {
            (0, ipc_1.setupIpcHandlers)(this.notesDB, this.clipboardManager, this.mainWindow);
        }
    }
    getMainWindow() {
        return this.mainWindow;
    }
    getNotesDB() {
        return this.notesDB;
    }
    getClipboardManager() {
        return this.clipboardManager;
    }
}
// Create the app instance
const nexusApp = new NexusApp();
exports.default = nexusApp;
