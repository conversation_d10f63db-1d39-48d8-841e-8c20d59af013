import React, { useEffect, useState, useCallback } from 'react';
import { NoteNode, SearchResult } from '../../shared/types';
import { TreeNode } from './TreeNode';

interface TreeSidebarProps {
  searchQuery: string;
  selectedNodeId?: string;
  onNodeSelect: (node: NoteNode) => void;
  onCreateNote?: (parentId?: string) => void;
  onCreateFolder?: (parentId?: string) => void;
}

export function TreeSidebar({
  searchQuery,
  selectedNodeId,
  onNodeSelect,
  onCreateNote,
  onCreateFolder
}: TreeSidebarProps) {
  const [tree, setTree] = useState<NoteNode[]>([]);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [editingNodeId, setEditingNodeId] = useState<string | null>(null);
  const [newlyCreatedNodeId, setNewlyCreatedNodeId] = useState<string | null>(null);

  // Load tree structure
  const loadTree = useCallback(async () => {
    try {
      setIsLoading(true);
      const treeData = await window.electronAPI.getNotesTree();
      console.log('Loaded tree data:', treeData.length, 'root items');
      setTree(treeData);
    } catch (error) {
      console.error('Error loading tree:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Search notes with debouncing
  const searchNotes = useCallback(async (query: string) => {
    try {
      setIsSearching(true);
      const results = await window.electronAPI.searchNotes(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching notes:', error);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Load tree on mount
  useEffect(() => {
    console.log('TreeSidebar mounted, loading tree...');
    console.log('window.electronAPI available on mount:', !!window.electronAPI);
    loadTree();
  }, [loadTree]);

  // Handle search with debouncing
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    const searchTimer = setTimeout(() => {
      searchNotes(searchQuery);
    }, 150); // 150ms debounce for responsive feel

    return () => {
      clearTimeout(searchTimer);
    };
  }, [searchQuery, searchNotes]);

  const handleToggleFolder = async (folderId: string) => {
    try {
      await window.electronAPI.toggleFolder(folderId);
      // Reload tree to get updated expand/collapse state
      await loadTree();
    } catch (error) {
      console.error('Error toggling folder:', error);
    }
  };

  // Instant note creation with inline editing
  const handleCreateNote = async (parentId?: string) => {
    console.log('handleCreateNote called with parentId:', parentId);
    console.log('window.electronAPI available:', !!window.electronAPI);
    console.log('window.electronAPI.createNote available:', !!window.electronAPI?.createNote);

    if (!window.electronAPI) {
      console.error('window.electronAPI is not available - running in browser mode?');
      alert('Electron API not available. Please run the app in Electron mode.');
      return;
    }

    try {
      console.log('About to call window.electronAPI.createNote...');
      // Create note immediately with temporary flag
      const noteId = await window.electronAPI.createNote('', 'Untitled', parentId, true);
      console.log('createNote returned noteId:', noteId);

      // Reload tree to get the new note
      console.log('About to reload tree...');
      await loadTree();
      console.log('Tree reloaded');

      // Use setTimeout to ensure the tree has been rendered before setting edit state
      setTimeout(() => {
        // Mark as newly created for animation
        setNewlyCreatedNodeId(noteId);
        setTimeout(() => setNewlyCreatedNodeId(null), 2000);

        // Enter edit mode for the new note
        setEditingNodeId(noteId);
        console.log('Edit mode set for nodeId:', noteId);
      }, 50); // Small delay to ensure tree rendering

      console.log('Created new temporary note for immediate editing:', noteId);
      console.log('Current tree state:', tree.length, 'items');
    } catch (error) {
      console.error('Error creating note:', error);
      console.error('Error details:', error.message, error.stack);
    }
  };

  // Instant folder creation with inline editing
  const handleCreateFolder = async (parentId?: string) => {
    console.log('handleCreateFolder called with parentId:', parentId);
    console.log('window.electronAPI available:', !!window.electronAPI);
    console.log('window.electronAPI.createFolder available:', !!window.electronAPI?.createFolder);

    if (!window.electronAPI) {
      console.error('window.electronAPI is not available - running in browser mode?');
      alert('Electron API not available. Please run the app in Electron mode.');
      return;
    }

    try {
      console.log('About to call window.electronAPI.createFolder...');
      // Create folder immediately with temporary flag
      const folderId = await window.electronAPI.createFolder('New Folder', parentId, true);
      console.log('createFolder returned folderId:', folderId);

      // Reload tree to get the new folder
      console.log('About to reload tree...');
      await loadTree();
      console.log('Tree reloaded');

      // Use setTimeout to ensure the tree has been rendered before setting edit state
      setTimeout(() => {
        // Mark as newly created for animation
        setNewlyCreatedNodeId(folderId);
        setTimeout(() => setNewlyCreatedNodeId(null), 2000);

        // Enter edit mode for the new folder
        setEditingNodeId(folderId);
        console.log('Edit mode set for folderId:', folderId);
      }, 50); // Small delay to ensure tree rendering

      console.log('Created new temporary folder for immediate editing:', folderId);
      console.log('Current tree state:', tree.length, 'items');
    } catch (error) {
      console.error('Error creating folder:', error);
      console.error('Error details:', error.message, error.stack);
    }
  };

  const handleDeleteNode = async (nodeId: string) => {
    try {
      await window.electronAPI.deleteNote(nodeId, true); // Delete with children
      await loadTree();
    } catch (error) {
      console.error('Error deleting node:', error);
    }
  };

  const handleRenameNode = async (nodeId: string, newName: string) => {
    try {
      // Find the node to determine if it's a folder or note
      const findNode = (nodes: NoteNode[]): NoteNode | null => {
        for (const node of nodes) {
          if (node.id === nodeId) return node;
          const found = findNode(node.children);
          if (found) return found;
        }
        return null;
      };

      const node = findNode(tree);
      if (node) {
        if (node.isFolder) {
          // For folders, update the title (content is empty for folders)
          await window.electronAPI.updateNote(nodeId, '', newName);
        } else {
          // For notes, update the title but keep content
          await window.electronAPI.updateNote(nodeId, node.content, newName);
        }
        await loadTree();
      }
    } catch (error) {
      console.error('Error renaming node:', error);
    }
  };

  // Inline editing handlers
  const handleStartEdit = (nodeId: string) => {
    console.log('Starting edit for node:', nodeId);
    setEditingNodeId(nodeId);
  };

  const handleFinishEdit = async (nodeId: string, newName: string) => {
    try {
      const trimmedName = newName.trim();
      
      if (!trimmedName) {
        // Empty name - cancel edit
        handleCancelEdit(nodeId);
        return;
      }

      // Basic name validation - prevent problematic characters
      const invalidChars = /[<>:"/\\|?*]/;
      if (invalidChars.test(trimmedName)) {
        alert('Name cannot contain the following characters: < > : " / \\ | ? *');
        return; // Keep editing mode active
      }

      // Prevent names that are too long
      if (trimmedName.length > 255) {
        alert('Name is too long. Please use a shorter name (maximum 255 characters).');
        return; // Keep editing mode active
      }

      // Find the current node and its siblings for duplicate checking
      const findNodeAndSiblings = (nodes: NoteNode[], targetId: string): { node: NoteNode | null, siblings: NoteNode[] } => {
        for (const node of nodes) {
          if (node.id === targetId) {
            return { node, siblings: nodes };
          }
          const result = findNodeAndSiblings(node.children, targetId);
          if (result.node) return result;
        }
        return { node: null, siblings: [] };
      };

      const { node, siblings } = findNodeAndSiblings(tree, nodeId);
      
      if (!node) {
        console.error('Node not found:', nodeId);
        setEditingNodeId(null);
        return;
      }

      // Check for duplicate names among siblings (case-insensitive)
      const duplicateExists = siblings.some(sibling => 
        sibling.id !== nodeId && 
        sibling.title.toLowerCase() === trimmedName.toLowerCase()
      );

      if (duplicateExists) {
        alert(`A ${node.isFolder ? 'folder' : 'note'} with the name "${trimmedName}" already exists in this location.`);
        return; // Keep editing mode active
      }

      // Update the note/folder name
      if (node.isFolder) {
        await window.electronAPI.updateNote(nodeId, '', trimmedName);
      } else {
        await window.electronAPI.updateNote(nodeId, node.content, trimmedName);
      }
      
      await loadTree();
      setEditingNodeId(null);
      console.log('Finished editing node:', nodeId, 'with name:', trimmedName);
    } catch (error) {
      console.error('Error finishing edit:', error);
      setEditingNodeId(null);
    }
  };

  const handleCancelEdit = async (nodeId: string) => {
    try {
      // Check if this was a newly created item by looking for temporary flag
      const findNode = (nodes: NoteNode[]): NoteNode | null => {
        for (const node of nodes) {
          if (node.id === nodeId) return node;
          const found = findNode(node.children);
          if (found) return found;
        }
        return null;
      };

      const node = findNode(tree);
      if (node?.isTemporary) {
        // Delete the temporary item
        await window.electronAPI.deleteNote(nodeId, true);
        await loadTree();
        console.log('Cancelled edit and deleted temporary node:', nodeId);
      }
      
      setEditingNodeId(null);
    } catch (error) {
      console.error('Error cancelling edit:', error);
      setEditingNodeId(null);
    }
  };

  const handleSearchResultClick = (result: SearchResult) => {
    // Create a temporary NoteNode from SearchResult
    const noteNode: NoteNode = {
      id: result.id,
      title: result.title,
      content: result.content,
      parentId: null,
      children: [],
      isFolder: false,
      isExpanded: false,
      isPinned: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    onNodeSelect(noteNode);
    // Clear search to show the tree again
    // This would need to be communicated back to parent component
  };

  // If searching, show search results
  if (searchQuery.trim()) {
    return (
      <div className="flex flex-col h-full">
        {/* Search Results Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              Search Results ({searchResults.length})
            </h2>
            <button
              onClick={() => {
                console.log('Search New Note button clicked');
                handleCreateNote();
              }}
              className="btn-primary text-sm py-1 px-3"
            >
              New Note
            </button>
          </div>
        </div>

        {/* Search Results List */}
        <div className="flex-1 overflow-y-auto custom-scrollbar">
          {isSearching ? (
            <div className="p-4 text-center text-gray-500">
              Searching...
            </div>
          ) : searchResults.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              No results found for &quot;{searchQuery}&quot;
            </div>
          ) : (
            searchResults.map((result) => (
              <div
                key={result.id}
                onClick={() => handleSearchResultClick(result)}
                className="p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <div className="font-medium text-gray-900 text-truncate">
                  {result.title}
                </div>
                {result.snippet && (
                  <div
                    className="text-sm text-gray-600 mt-1"
                    dangerouslySetInnerHTML={{ __html: result.snippet }}
                  />
                )}
                {result.path.length > 0 && (
                  <div className="text-xs text-gray-400 mt-2 flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                    </svg>
                    {result.path.join(' > ')}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    );
  }

  // Normal tree view
  console.log('TreeSidebar render:', { searchQuery, tree: tree.length, searchResults: searchResults.length, isLoading, isSearching });

  return (
    <div className="w-80 border-r border-gray-200 bg-white flex flex-col h-full" style={{minWidth: '320px'}}>
      {/* Tree Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Notes (Tree)</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => {
                console.log('New Folder button clicked');
                alert('New Folder button clicked! Check console for details.');
                handleCreateFolder();
              }}
              className="btn-secondary text-sm py-1 px-3"
              title="New Folder"
            >
              📁
            </button>
            <button
              onClick={() => {
                console.log('New Note button clicked');
                alert('New Note button clicked! Check console for details.');
                handleCreateNote();
              }}
              className="btn-primary text-sm py-1 px-3"
            >
              New Note
            </button>
          </div>
        </div>
      </div>

      {/* Tree View */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        {isLoading ? (
          <div className="p-4 text-center text-gray-500">
            Loading notes...
          </div>
        ) : tree.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            No notes yet. Create your first note!
          </div>
        ) : (
          <div className="py-2">
            {tree.map((node) => (
              <TreeNode
                key={node.id}
                node={node}
                level={0}
                selectedNodeId={selectedNodeId}
                editingNodeId={editingNodeId || undefined}
                newlyCreatedNodeId={newlyCreatedNodeId || undefined}
                onNodeSelect={onNodeSelect}
                onToggleFolder={handleToggleFolder}
                onCreateNote={handleCreateNote}
                onCreateFolder={handleCreateFolder}
                onDeleteNode={handleDeleteNode}
                onRenameNode={handleRenameNode}
                onStartEdit={handleStartEdit}
                onFinishEdit={handleFinishEdit}
                onCancelEdit={handleCancelEdit}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}