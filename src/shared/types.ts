// Shared types for the Nexus application

export interface Note {
  id: string;
  content: string;
  title?: string;
  created_at: string;
  updated_at: string;
  parent_id?: string | null;  // null for root notes
  is_folder?: boolean;        // true for folders, false for notes
  is_expanded?: boolean;      // for folder collapse/expand state
  is_pinned?: boolean;       // for pinned notes
  is_temporary?: boolean;    // for cleanup if user cancels
}

// Tree node interface for UI components
export interface NoteNode {
  id: string;
  title: string;
  content: string;
  parentId: string | null;
  children: NoteNode[];
  isFolder: boolean;
  isExpanded: boolean;
  isPinned: boolean;
  isTemporary?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Search result with path information
export interface SearchResult {
  id: string;
  title: string;
  content: string;
  snippet?: string;
  path: string[];
  matchType: 'title' | 'content';
}

export interface Folder {
  id: string;
  name: string;
  parent_id?: string;
}

export interface ClipboardItem {
  id: string;
  content: string;
  type: 'text' | 'image' | 'file';
  created_at: string;
}

export interface AppState {
  currentView: 'notes' | 'calculator' | 'clipboard';
  selectedNote?: Note;
  searchQuery: string;
}

// Electron API types
export interface ElectronAPI {
  // Notes operations
  createNote: (content: string, title?: string, parentId?: string, isTemporary?: boolean) => Promise<string>;
  createFolder: (name: string, parentId?: string, isTemporary?: boolean) => Promise<string>;
  updateNote: (id: string, content: string, title?: string) => Promise<void>;
  deleteNote: (id: string, deleteChildren?: boolean) => Promise<void>;
  moveNote: (noteId: string, newParentId: string | null) => Promise<void>;
  toggleFolder: (folderId: string) => Promise<void>;
  searchNotes: (query: string) => Promise<SearchResult[]>;
  getAllNotes: () => Promise<Note[]>;
  getNotesTree: () => Promise<NoteNode[]>;
  getNotePath: (noteId: string) => Promise<string[]>;
  
  // Clipboard operations
  getClipboardHistory: () => Promise<ClipboardItem[]>;
  clearClipboardHistory: () => Promise<void>;
  copyToClipboard: (content: string) => Promise<void>;
  removeClipboardItem: (id: string) => Promise<void>;
  searchClipboardHistory: (query: string) => Promise<ClipboardItem[]>;
  
  // Window operations
  hideWindow: () => void;
  showWindow: () => void;
  toggleWindow: () => void;
  
  // App operations
  quit: () => void;
}

// Electron Events interface
export interface ElectronEvents {
  onCreateNewNote: (callback: () => void) => void;
  onFocusSearch: (callback: () => void) => void;
  onSwitchToCalculator: (callback: () => void) => void;
  onSwitchToClipboard: (callback: () => void) => void;
  onShowPreferences: (callback: () => void) => void;
  removeAllListeners: (channel: string) => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
    electronEvents: ElectronEvents;
  }
}
