import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { ElectronAPI } from '../shared/types';

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const electronAPI: ElectronAPI = {
  // Notes operations
  createNote: (content: string, title?: string, parentId?: string, isTemporary?: boolean) => 
    ipcRenderer.invoke('notes:create', content, title, parentId, isTemporary),
  
  createFolder: (name: string, parentId?: string, isTemporary?: boolean) =>
    ipcRenderer.invoke('notes:createFolder', name, parentId, isTemporary),
  
  updateNote: (id: string, content: string, title?: string) => 
    ipcRenderer.invoke('notes:update', id, content, title),
  
  deleteNote: (id: string, deleteChildren?: boolean) => 
    ipcRenderer.invoke('notes:delete', id, deleteChildren),
    
  moveNote: (noteId: string, newParentId: string | null) =>
    ipcRenderer.invoke('notes:move', noteId, newParentId),
    
  toggleFolder: (folderId: string) =>
    ipcRenderer.invoke('notes:toggleFolder', folderId),
  
  searchNotes: (query: string) => 
    ipcRenderer.invoke('notes:search', query),
  
  getAllNotes: () => 
    ipcRenderer.invoke('notes:getAll'),
    
  getNotesTree: () =>
    ipcRenderer.invoke('notes:getTree'),
    
  getNotePath: (noteId: string) =>
    ipcRenderer.invoke('notes:getPath', noteId),

  // Clipboard operations
  getClipboardHistory: () =>
    ipcRenderer.invoke('clipboard:getHistory'),

  clearClipboardHistory: () =>
    ipcRenderer.invoke('clipboard:clear'),

  copyToClipboard: (content: string) =>
    ipcRenderer.invoke('clipboard:copy', content),

  removeClipboardItem: (id: string) =>
    ipcRenderer.invoke('clipboard:remove', id),

  searchClipboardHistory: (query: string) =>
    ipcRenderer.invoke('clipboard:search', query),

  // Window operations
  hideWindow: () => 
    ipcRenderer.invoke('window:hide'),
  
  showWindow: () => 
    ipcRenderer.invoke('window:show'),
  
  toggleWindow: () => 
    ipcRenderer.invoke('window:toggle'),

  // App operations
  quit: () => 
    ipcRenderer.invoke('app:quit'),
};

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', electronAPI);
  } catch (error) {
    console.error('Failed to expose electronAPI:', error);
  }
} else {
  // @ts-ignore (define in dts file)
  window.electronAPI = electronAPI;
}

// Also expose some event listeners for the renderer
contextBridge.exposeInMainWorld('electronEvents', {
  // Listen for events from main process
  onCreateNewNote: (callback: () => void) => {
    ipcRenderer.on('create-new-note', callback);
  },
  
  onFocusSearch: (callback: () => void) => {
    ipcRenderer.on('focus-search', callback);
  },
  
  onSwitchToCalculator: (callback: () => void) => {
    ipcRenderer.on('switch-to-calculator', callback);
  },
  
  onSwitchToClipboard: (callback: () => void) => {
    ipcRenderer.on('switch-to-clipboard', callback);
  },
  
  onShowPreferences: (callback: () => void) => {
    ipcRenderer.on('show-preferences', callback);
  },

  // Remove listeners
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
});
