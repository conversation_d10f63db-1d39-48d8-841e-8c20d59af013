@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Nexus */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
  }

  /* Button styles */
  .btn-primary {
    @apply bg-nexus-500 hover:bg-nexus-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow;
  }
  
  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-all duration-200 border border-gray-200 hover:border-gray-300;
  }
  
  .btn-ghost {
    @apply hover:bg-gray-100 text-gray-600 hover:text-gray-800 font-medium py-2 px-3 rounded-lg transition-all duration-200;
  }

  /* Input styles */
  .input-primary {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-nexus-500 focus:border-transparent transition-all duration-200;
  }
  
  .input-search {
    @apply w-full px-4 py-2.5 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-nexus-500 focus:border-transparent focus:bg-white placeholder-gray-400 transition-all duration-200 text-sm;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100;
  }
  
  .card-hover {
    @apply card hover:shadow-md hover:border-gray-200 transition-all duration-200 cursor-pointer;
  }

  /* Note item styles */
  .note-item {
    @apply p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors duration-150;
  }
  
  .note-item:last-child {
    @apply border-b-0;
  }
  
  .note-item.selected {
    @apply bg-nexus-50 border-l-4 border-l-nexus-500;
  }

  /* Tree node styles */
  .newly-created {
    @apply bg-blue-50 border-l-2 border-l-blue-300 animate-pulse;
    animation-duration: 2s;
    animation-iteration-count: 2;
  }
  
  .temporary-item {
    @apply bg-yellow-50 border-l-2 border-l-yellow-300;
  }
  
  .editing-item {
    @apply bg-blue-50 border-l-2 border-l-blue-500 shadow-sm;
  }

  /* Editor styles */
  .editor-textarea {
    @apply w-full h-full resize-none border-none outline-none bg-transparent font-mono text-sm leading-relaxed;
  }

  /* Sidebar styles */
  .sidebar {
    @apply w-64 bg-white border-r border-gray-200 flex flex-col;
  }
  
  .sidebar-header {
    @apply p-4 border-b border-gray-200 bg-gray-50;
  }
  
  .sidebar-content {
    @apply flex-1 overflow-y-auto custom-scrollbar;
  }

  /* Navigation styles */
  .nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-150;
  }
  
  .nav-item.active {
    @apply text-nexus-600 bg-nexus-50;
  }

  /* Utility classes */
  .text-truncate {
    @apply truncate;
  }
  
  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Prevent text selection */
  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* macOS-style backdrop blur */
  .backdrop-blur-macos {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }
}
